// Copyright Isto Inc.

using Zenject;

namespace Isto.StyleSheetExample
{
    // The larger sections of a class are in order: Types, Fields, Properties, Events, Methods, but we have some exceptions.
    // 1. The specific sections go: Types, Inspector-visible fields*, Fields, Properties, Events, Injected fields,
    //                              Lifecycle*, Event handling*, Accessors, Other methods
    // 2. Then each section goes const, static readonly, static, nonstatic.
    // 3. Then by default each of those are in order of public, internal, protected, private scope. I marked exceptions with a *
    // (Note that you can omit a section name if it has nothing in it.)
    public class ClassOrderingExample
    {
        // inner type declarations here (enums, classes, interfaces)
        // discouraged if type is public (it should have its own file)


        // UNITY HOOKUP *
        // [SerializeField] first
        // publics after


        // OTHER FIELDS
        // […]


        // PROPERTIES
        // […]


        // EVENTS
        // includes delegates too
        

        // INJECTION
        // this section is an exception to the OTHER FIELDS section

        [Inject]
        public void Inject(/*put the interfaces first*/)
        {
            // [...]
        }


        // LIFECYCLE EVENTS *
        // for monobehaviors see expected monobehavior events in relevant wiki section or unity doc
        //     (https://docs.unity3d.com/Manual/ExecutionOrder.html)
        // for other types, if they have a defined set of lifecycle events then put those
        //     e.g. for a state machine, Enter, Exit, Run
        // otherwise, put C# constructor and destructor
        //
        // try to keep them togther by lifecycle scope (then by expected order)
        //     e.g. Awake, OnDestroy, OnEnable, OnDisable, Start, Update


        // EVENT HANDLING *
        // put registration first, no mater the scope
        private void RegisterEvents()
        {
            // [...]
        }

        private void UnregisterEvents()
        {
            // [...]
        }

        // then put event handlers, in normal order


        // ACCESSORS
        // any get, set, or Is method goes here


        // OTHER METHODS
        // you are encouraged to do logical grouping where it makes sense e.g. implementations of the same interface


        // OTHER: Some logical grouping

        //[...]


        // OTHER: Some other grouping

        //[...]
    }
}