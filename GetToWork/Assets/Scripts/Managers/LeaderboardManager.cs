// Copyright Isto Inc.

using Isto.GTW.Data;
using Isto.GTW.Leaderboards;
using Steamworks;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.GTW.Managers
{
    public class LeaderboardManager : MonoBehaviour, ILeaderboards
    {
        [Header("Steamworks Leaderboard Name")]
        [SerializeField]
        private string _leaderboardName = "DoinklerSpecial_HighestCheckpoint";

        private CallResult<LeaderboardScoresDownloaded_t> _downloadScoresResult;
        private CallResult<LeaderboardFindResult_t>       _findLeaderboardResult;
        private SteamLeaderboard_t _steamLeaderboard;
        private CallResult<LeaderboardScoreUploaded_t> _uploadScoreResult;
        public event Action<List<LeaderboardEntryData>> OnCheckpointsDownloaded;
        private bool _leaderboardFound;


        private void Start()
        {
            if (!SteamManager.Initialized)
            {
                Debug.LogError("[LeaderboardManager] SteamManager not initialized. Cannot find leaderboard.");
                return;
            }

            _findLeaderboardResult    = CallResult<LeaderboardFindResult_t>.Create(OnFindLeaderboard);
            _uploadScoreResult        = CallResult<LeaderboardScoreUploaded_t>.Create(OnUploadScore);
            _downloadScoresResult     = CallResult<LeaderboardScoresDownloaded_t>.Create(OnDownloadScores);

            SteamUserStats.RequestCurrentStats();

            SteamAPICall_t handle = SteamUserStats.FindLeaderboard(_leaderboardName);
            _findLeaderboardResult.Set(handle);

            Debug.Log($"[LeaderboardManager] Attempting to find leaderboard '{_leaderboardName}'...");
        }

        private void OnFindLeaderboard(LeaderboardFindResult_t result, bool isIOFailure)
        {
            if (isIOFailure || result.m_bLeaderboardFound == 0)
            {
                Debug.LogError($"[LeaderboardManager] Failed to find leaderboard '{_leaderboardName}'. IOFailure? {isIOFailure}");
                return;
            }

            _steamLeaderboard = result.m_hSteamLeaderboard;
            _leaderboardFound = true;
            Debug.Log($"[LeaderboardManager] Found leaderboard '{_leaderboardName}' (Handle: { _steamLeaderboard.m_SteamLeaderboard }).");
        }

        private void OnUploadScore(LeaderboardScoreUploaded_t result, bool isIOFailure)
        {
            if (isIOFailure || result.m_bSuccess == 0)
            {
                Debug.LogError($"[LeaderboardManager] Score upload failed. IOFailure? {isIOFailure}");
                return;
            }

            Debug.Log($"[LeaderboardManager] Upload succeeded. New rank: {result.m_nGlobalRankNew}, Score: {result.m_nScore}");

            DownloadFriendsEntries(1, 10);
        }

        private void OnDownloadScores(LeaderboardScoresDownloaded_t result, bool isIOFailure)
        {
            if (isIOFailure)
            {
                Debug.LogError($"[LeaderboardManager] Failed to download leaderboard entries. IOFailure? {isIOFailure}");
                return;
            }

            int count = result.m_cEntryCount;

            var entries = new List<LeaderboardEntryData>(count);

            for (int i = 0; i < count; i++)
            {
                LeaderboardEntry_t entry;
                SteamUserStats.GetDownloadedLeaderboardEntry(
                    result.m_hSteamLeaderboardEntries,
                    i,
                    out entry,
                    null,
                    0
                );

                string playerName = SteamFriends.GetFriendPersonaName(entry.m_steamIDUser);

                int globalRank = entry.m_nGlobalRank;
                int localRank;
                localRank = i + 1;

                // Create our data object with both ranks:
                var data = new LeaderboardEntryData(
                    entry.m_steamIDUser,
                    playerName,
                    entry.m_nScore,
                    globalRank,
                    localRank
                );
                entries.Add(data);
            }

            // Fire the event so your UI can consume the list:
            OnCheckpointsDownloaded?.Invoke(entries);
        }

        private void OnDestroy()
        {
            _findLeaderboardResult?.Cancel();
            _uploadScoreResult?.Cancel();
            _downloadScoresResult?.Cancel();
        }

        public void UploadHighestCheckpoint(int score)
        {
            if (!_leaderboardFound)
            {
                Debug.LogError("[LeaderboardManager] UploadScore called too early. Leaderboard not yet found.");
                return;
            }

            var call = SteamUserStats.UploadLeaderboardScore(
                _steamLeaderboard,
                ELeaderboardUploadScoreMethod.k_ELeaderboardUploadScoreMethodKeepBest,
                score,
                null, 0
            );
            _uploadScoreResult.Set(call);
            Debug.Log($"[LeaderboardManager] Uploading score {score}...");
        }

        public void DownloadFriendsEntries(int startRank, int endRank)
        {
            if (!_leaderboardFound)
            {
                Debug.LogError("[LeaderboardManager] DownloadTopEntries called too early. Leaderboard not yet found.");
                return;
            }

            var call = SteamUserStats.DownloadLeaderboardEntries(
                _steamLeaderboard,
                ELeaderboardDataRequest.k_ELeaderboardDataRequestFriends,
                startRank,
                endRank
            );

            _downloadScoresResult.Set(call);
            Debug.Log($"[LeaderboardManager] Requesting entries {startRank}–{endRank}...");
        }
    }
}