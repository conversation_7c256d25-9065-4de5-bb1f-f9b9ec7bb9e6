// Copyright Isto Inc.

// STYLE SHEET EXAMPLE - C# - Unity

// References:
// - This is an example Style Guide adapted from: https://github.com/thomasjacobsen-unity/Unity-Code-Style-Guide
// - <PERSON>'s style guide is found at: https://unity.com/how-to/naming-and-code-style-tips-c-scripting-unity
// - Microsoft's Framework Design Guidelines: https://docs.microsoft.com/en-us/dotnet/standard/design-guidelines/

// General:
// - These rules are customized to build our own style guide. When in doubt, our guide prevails.
// - If something is not explicitly defined, you can refer to Microsoft's guidelines.
// - Developer discretion allowed, but if you need to veer off the guidelines, ask for a second opinion.
// - If you had to decide a rule to cover an unexplored situation, bring it to the next dev team meeting.

// Source Control:
// - Branch style: "feature/MTM-201-branch-name" or "bug/MTM-201-branch-name" (as per bitbucket auto-generated style).
// - Commit style: "[MTM-201] Changed ripe bananas for ripe plantains in story mode scene".
// - Always delete your branch after merging it.

// Naming/Casing:
// - Use Pascal case (e.g. ExamplePlayerController, MaxHealth, etc.) unless noted otherwise.
// - Use camel case (e.g. examplePlayerController, maxHealth, etc.) for local/public variables, parameters.
// - Use an underscore prefix (e.g. _examplePlayerController) for private variables.
// - Use all caps snake case (e.g. EXAMPLE_PLAYER_CONTROLLER) for consts, static readonly variables.

// Formatting:
// - Use UTF-8
// - Keep lines short. Do not exceed 120 characters (hard cap).
// - Do use a single horizontal spacing line before and after each class, multiline property and method implementation.
//   - Do include the comments that document a class, property or method next to them, and bump the spacing above it.
// - Use single horizontal spacing lines to break up field declarations and method contents to do logical grouping.
//   - Use a single horizontal spacing line before each comment.
//   - Do not add spacing after an opening bracket or before a closing one, the bracket provides the spacing.

// The following are specified in your EditorConfig file, but should be fixed via a popup when you open a file.
// - Do not use tabulations (use 4 whitespaces instead).
// - Do use windows style line breaks (CR/LF).

// The following should be auto-formatted correctly from your EditorConfig file:
//   - Use Allman (opening curly braces on a new line) style braces.
//   - Do not use column alignment, spacing around symbols and operators should be 1 character wide.
//     - Column alignment is acceptable if needed for readability, but running the Code Cleanup command will ruin it.
//   - Use a single space before flow control conditions, e.g. while (x == y).
//   - Avoid spaces inside brackets, e.g. x = dataArray[index].
//   - Use a single space after a comma between function arguments.
//   - Don’t add a space after the parenthesis and function arguments, e.g. CollectItem(myObject, 0, 1);
//   - Don’t use spaces between a function name and parenthesis, e.g. DropPowerUp(myPrefab, 0, 1);

// The Isto .editorconfig file should be found in the root of the Core submodule, next to this file.
// - This means it will apply to C# files in the Core module.
// - Put a copy of it in your project root and it will apply to all files in the project.
// The formatting of existing code isn't changed unless you run one of the following commands in Visual Studio:
// - Code Cleanup (Ctrl+K, Ctrl+E), which applies:
//   - Any white-space settings, such as indent style.
//   - Selected code style settings, such as how to sort using directives.
// - Edit > Advanced > Format Document(or Ctrl + K, Ctrl + D in the default profile), which:
//   - Only applies white-space settings, such as indent style.

// Comments:
// - Make sure the standard copyright disclaimer is at the top of your file ( "Copyright Isto Inc." ).
// - The code should be self-explanatory and clear enough to define the "what" or the "how".
// - Comments can fill in the gaps and tell us "why."
// - Use the // comment to keep the explanation next to the logic (can be on same line if under 120 characters).
// - Use a Tooltip instead of a comment for serialized fields.
// - Avoid Regions. They encourage large class sizes. Collapsed code is more difficult to read.
// - Use a /// comment with summary XML tag in front of classes, methods or functions for Intellisense.
//   - Add documentation anywhere context is needed (public or private).
//   - While writing style is up to the individual, the description contained in the summary should be formal:
//     - Use full sentences.
//     - Be concise.
//   - Also define parameters and return values when pertinent.

// Formatting example for XML tags on methods (don't pay attention to the logic, it's a poor example in that sense):
/// <summary>
/// This method verifies if a banana is ripe.
/// </summary>
/// <param name="type">The type of banana to evaluate.</param>
/// <returns>True if the banana is ripe.</returns>
//public bool IsBananaRipe(BananaType type)
//{
//    return type == BananaType.Ripe;
//}

// - Avoid leaving TODOs in the code if possible. Only use if it is work that is needed now.
//   - If you do, add a name, date and jira task ID if possible, for accountability and context.
// - If future work is really important, it should be a new task in jira.

// Example:
// TODO: FG.2023-09-11 [MTM-135] Add handling here for the other cases once designer tests and accepts the feature

// Using Statements:
// - Listed before namespace.
// - Keep usings ordered, and remove if unused.
// - Running the command "Remove and sort usings" in VS after you write your changes should do this for you.
//   - Default hotkey is ctrl+R, ctrl+G
//   - Test it here and System.Collections should be removed from the list.
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

// Namespaces:
// - Pascal case, without special symbols or underscores.
// - Add using line at the top to avoid typing namespace repeatedly.
// - Convention is CompanyName.ProjectName, and maps to the main Scripts folder of the project (or submodule).
// - At your discretion, sections that intend to be modular can be in CompanyName.ProjectName.FirstSubfolder
namespace Isto.StyleSheetExample
{
    // Classes or Structs:
    // - Name them with nouns or noun phrases.
    // - Prefer avoiding prefixes.
    // - More info on prefixes can be found in OneNote: IstoInc-Wiki > Code Style > Syntax > Prefixes for Classes
    public class CodeStyleExample : MonoBehaviour
    {
        // Other type declarations:
        // - Prefer making them inner if exclusively needed inside the main declaration (or almost exclusively).
        //   - They go first in the declarations order (before any section headers), and probably should be private.
        // - Prefer to define outside the main type if the other type is shared or widely available.
        //   - They go in their own file.
        //   - If other conventions prevent this, then define all other types before the file-name-sharing type.
        //     - Example: Job declarations in ECS.

        // This example is a serializable class that groups fields in the Inspector.
        [Serializable]
        private struct PlayerStats
        {
            public int movementSpeed;
            public int hitPoints;
            public bool hasHealthPotion;
        }

        // Enums:
        // - Use a singular type name with the Enum suffix.
        // - If the enum is defined in its own file, the file name should be the same as the enum name.
        private enum DirectionEnum
        {
            North,
            South,
            East,
            West,
        }

        // Flags Enums:
        // - Use a singular type name with the Flags suffix.
        [Flags]
        private enum AttackModeFlags
        {
            None = 0 << 0,
            Melee = 1 << 0,
            Ranged = 1 << 1,
            Special = 1 << 2,

            MeleeAndSpecial = Melee | Special
        }

        // Interfaces:
        // - Prefer naming interfaces with adjectives.
        // - Use the 'I' prefix.
        private interface IDamageable
        {
            string DamageTypeName { get; }
            float DamageValue { get; }

            // Methods:
            // - Start a method name with a verb or verb phrase to show an action.
            // - Parameter names are camelCase.
            bool ApplyDamage(string description, float damage, int numberOfHits);
        }


        // UNITY HOOKUP

        // - Prefer keeping field attributes on the same line as the variable if possible.
        //   - Exceptions for Header, Space and Tooltip who always have their own line.
        // - In non-field situations, prefer using one line per attribute.

        // Use [SerializeField] attribute if and only if you want to display a private field in Inspector.
        // Booleans ask a question that can be answered true or false.
        [SerializeField] private bool _isPlayerDead;

        // This groups data from the custom PlayerStats class in the Inspector.
        [Space(20)]
        [Header("Statistics")]
        [SerializeField] private PlayerStats _stats;

        // This limits the values to a Range and creates a slider in the Inspector.
        [Range(0f, 1f)] [SerializeField] private float _rangedStat;

        // A tooltip can replace a comment on a serialized field and do double duty.
        [Tooltip("This is another statistic for the player.")]
        [SerializeField] private float _anotherStat;


        // OTHER FIELDS

        // Constants:
        // - Avoid defining consts, prefer defining constants as static readonly variables.
        // - Name them using all caps snake case.
        // - Prefer defining constants instead of having literals in methods.
        //   - Value clarity of meaning/intent over clarity of inner workings.
        private static readonly int MAX_HEALTH_MINIMUM = 50;
        private static readonly int MAX_HEALTH_CAP = 999;
        private static readonly string DEATH_MESSAGE = "You are dead.";
        private static readonly int DOOR_OPENED_OFFSET = 123;

        // Fields:
        // - Prefer avoiding public fields, unless you are writing a simple data holding object.
        // - Always specify the default access modifier.
        // - Use nouns for names, but prefix booleans with a verb.
        // - Use meaningful names. Make names searchable and pronounceable. Don’t abbreviate (unless it’s math).
        // - Use camel case, without special characters.
        // - Add an underscore (_) in front of private fields to differentiate from local variables.
        private int _elapsedTimeInDays;

        // Properties:
        // - Preferable to a public field.
        // - Pascal case, without special characters.
        // - Use the expression-bodied properties to shorten.
        //   - e.g. use expression-bodied for read-only properties but { get; set; } for everything else.
        // - Use the Auto-Implemented Property for a public property without a backing field.

        // the private backing field
        private int _maxHealth = 99;


        // PROPERTIES

        // Expression bodied: read-only, returns backing field or any other relevant value - preferred writing style.
        public int MaxHealthReadOnly => _maxHealth;

        // Expression getter and setter - to be avoided.
        //public int MaxHealth
        //{
        //    get => _maxHealth;
        //    set => _maxHealth = value;
        //}

        // Auto-implemented with defined accessors - if you can't rely on expression body for your needs.
        public int Health { private get; set; }

        // Explicitly implemented getter and setter - preferred if a small amount of logic is needed.
        //   - Prefer a method if you're adding heavy logic in the getter or setter.
        public int MaxHealth
        {
            get
            {
                return _maxHealth;
            }
            set
            {
                _maxHealth = Mathf.Clamp(value, MAX_HEALTH_MINIMUM, MAX_HEALTH_CAP);
            }
        }


        // EVENTS

        // - Name with a verb phrase.
        // - Present participle means "before" and past participle mean "after".
        // - Naming scheme:
        //   - event/action = "OnOpeningDoor"
        //   - event raising method = "FireDoorOpened"
        //   - event handling method = "MySubject_DoorOpened"
        // - If you're simply passing the values/references for immediate use, prefer a System.Action delegate.
        //   - Actions should be appropriate for most events and can take 0 to 16 parameters.
        // - If Actions are too hard to read because of complex or numerous types, or otherwise need context, consider
        //   using a custom XyzEventArg (just a struct, no need to inherit from System.EventArgs).
        //   - This is still for immediate use only.
        // - If you are using a data struct in a larger scope than the event, then pass it in the Action parameters.
        // - Do not use System.EventHandler (prefer strongly typed parameters).
        // - Prefer not using unity events (do not hook up events in inspector if you can avoid it).
        //   - If you cannot avoid it, leave a comment stating the usage is in Editor and explain where to find it.
        // - Consider the monolithic project level event manager for events that:
        //   - Have many sources spread across the game.
        //   - Are very project specific.
        // A more detailed example of events code is available in OneNote: IstoInc-Wiki > Code Style > Events Example

        public event Action OnOpeningDoor; // event before
        public event Action OnDoorOpened; // event after
        public event Action<int> OnPointsScored;


        // EVENT HANDLING

        // Observers should follow this pattern of mirrored register/unregister methods if possible.
        public void RegisterEvents()
        {
            OnDoorOpened += StyleExample_OnDoorOpened;
        }

        public void UnregisterEvents()
        {
            OnDoorOpened -= StyleExample_OnDoorOpened;
        }

        // Names of event handlers tell you where the event comes from.
        // Keep different handlers for different sources, but don't duplicate logic.
        private void StyleExample_OnDoorOpened()
        {
            // Door opened logic is in a method
            DoSomething(x: DOOR_OPENED_OFFSET);
        }

        // Events that are intended to be used in the editor and not in code need a comment explaining that.
        // Give a general idea of where you expect to find the event if another developer needs to go looking for it.
        // Example below:

        // This event is used within the Unity Editor. The main prefab that uses this method is the RemoteDoor prefab
        // within the c_ButtonControl container.
        private void Button_OpenDoor()
        {
            // Door opened logic is in a method
            DoSomething(x: DOOR_OPENED_OFFSET);
        }


        // ACCESSORS

        // - Start a method's name with a verb to show an action.
        // - Parameter names are camel case.

        // Methods start with a verb.
        public void SetInitialPosition(float x, float y, float z)
        {
            transform.position = new Vector3(x, y, z);
        }

        // Methods ask a question when they return bool.
        public bool IsNewPositionReached(Vector3 newPosition)
        {
            return transform.position == newPosition;
        }


        // OTHER METHODS

        // Example event raising method.
        // Since events are intended to only be fired from inside the owning class by design, think about options
        // before you make these types of methods accessible publicly.
        private void FireDoorOpened()
        {
            OnDoorOpened?.Invoke();
        }

        private void FormatExamples(int someExpression)
        {
            // Var:
            // Can be used only if both are true.
            //   - It is assigned from a explicit type on the same line or is easy to infer.
            //   - The type is a little too complex/long (discretion is advised).
            int points = 8; // don't use var
            PlayerStats stats = new PlayerStats(); // don't use var
            List<PlayerStats> powerUps = new List<PlayerStats>(); // don't use var

            var dict = new Dictionary<string, List<GameObject>>(); // can be var, on the fence
            Dictionary<string, List<GameObject>> otherDict = GetRegisteredObjects(); // on the fence, but prefer not

            var keys = otherDict.Keys; // prefer var, type is pretty complex but the key type is easy to look up
            var keysEnumerator = otherDict.Keys.GetEnumerator(); // use var unless there is something weird to expose

            // Switch Statements:
            // - Always add default.
            // - Follow this indentation pattern (which the IDE should structure for you).
            switch (someExpression)
            {
                case 0:
                    // ...
                    break;
                case 1:
                    // ...
                    break;
                case 2:
                    // ...
                    break;
                default:
                    break;
            }

            // Braces:
            // - Avoid single-line statement entirely (for debuggability).
            // - Always keep braces.
            // - Exceptions:
            //   - Abort conditions, which are kept at the top of the method and have only one "return;" statement as
            //     their logic. These are without braces.
            //   - Consecutive using blocks with no logic other than the innermost brace, which are kept on one line
            //     for a flatter and easier to read declaration (see below).

            // Don't use single-line statements.
            for (int i = 0; i < 100; i++) { DoSomething(i); }

            // Don't omit braces.
            for (int i = 0; i < 100; i++)
                DoSomething(i);

            // This is correct.
            for (int i = 0; i < 100; i++)
            {
                DoSomething(i);
            }

            // This is also correct. Don't remove the braces.
            for (int i = 0; i < 10; i++)
            {
                for (int j = 0; j < 10; j++)
                {
                    DoSomething(j);
                }
            }

            // Abort conditions like this are correct but they need to be first in the method.
            if (powerUps == null)
                return;

            // Chained using statements sharing a scope seem to be another acceptable edge case for omitting brackets.
            using (WWW homePage = new WWW(@"https:\\foo.bar"))
            using (WWW settingsPage = new WWW(@"https:\\foo.bar/settings.htm"))
            {
                // ...
            }
        }

        private Dictionary<string, List<GameObject>> GetRegisteredObjects()
        {
            // ...
            return null;
        }

        private void DoSomething(int x)
        {
            // ...
        }
    }
} // Don't add extra empty lines at the end of the file.